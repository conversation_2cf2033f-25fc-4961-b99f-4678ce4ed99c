/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';
import { createSentryMock } from '$tests/mocks/sentry';

// Mock Sentry before any other imports
vi.mock('@sentry/sveltekit', () => createSentryMock());

// Mock data for RPC function testing
const mockOrganizations = [
	{ org_id: 'org-1', name: 'Organization 1' },
	{ org_id: 'org-2', name: 'Organization 2' },
];

const mockClients = [
	{ client_id: 'client-1', name: 'Client 1', org_id: 'org-1' },
	{ client_id: 'client-2', name: 'Client 2', org_id: 'org-1' },
	{ client_id: 'client-3', name: 'Client 3', org_id: 'org-2' },
];

const mockProjects = [
	{ project_id: 'project-1', name: 'Project 1', client_id: 'client-1' },
	{ project_id: 'project-2', name: 'Project 2', client_id: 'client-1' },
	{ project_id: 'project-3', name: 'Project 3', client_id: 'client-2' },
	{ project_id: 'project-4', name: 'Project 4', client_id: 'client-3' },
];

const mockMemberships = [
	// User 1 - Org admin of org-1
	{ user_id: 'user-1', entity_id: 'org-1', entity_type: 'organization', role: 'admin' },
	// User 2 - Client admin of client-1
	{ user_id: 'user-2', entity_id: 'client-1', entity_type: 'client', role: 'admin' },
	// User 3 - Project editor of project-1
	{ user_id: 'user-3', entity_id: 'project-1', entity_type: 'project', role: 'editor' },
	// User 4 - Multiple roles
	{ user_id: 'user-4', entity_id: 'org-2', entity_type: 'organization', role: 'admin' },
	{ user_id: 'user-4', entity_id: 'client-3', entity_type: 'client', role: 'admin' },
	{ user_id: 'user-4', entity_id: 'project-4', entity_type: 'project', role: 'editor' },
];

const mockVendors = [
	{
		vendor_id: 'vendor-1',
		name: 'Org Vendor 1',
		description: 'Organization level vendor',
		vendor_type: 'contractor',
		contact_name: 'John Doe',
		contact_email: '<EMAIL>',
		contact_phone: '******-0001',
		is_active: true,
		org_id: 'org-1',
		client_id: null,
		project_id: null,
	},
	{
		vendor_id: 'vendor-2',
		name: 'Client Vendor 1',
		description: 'Client level vendor',
		vendor_type: 'supplier',
		contact_name: 'Jane Smith',
		contact_email: '<EMAIL>',
		contact_phone: '******-0002',
		is_active: true,
		org_id: null,
		client_id: 'client-1',
		project_id: null,
	},
	{
		vendor_id: 'vendor-3',
		name: 'Project Vendor 1',
		description: 'Project level vendor',
		vendor_type: 'subcontractor',
		contact_name: 'Bob Johnson',
		contact_email: '<EMAIL>',
		contact_phone: '******-0003',
		is_active: true,
		org_id: null,
		client_id: null,
		project_id: 'project-1',
	},
	{
		vendor_id: 'vendor-4',
		name: 'Inactive Vendor',
		description: 'Inactive vendor',
		vendor_type: 'contractor',
		contact_name: 'Alice Brown',
		contact_email: '<EMAIL>',
		contact_phone: '******-0004',
		is_active: false,
		org_id: 'org-1',
		client_id: null,
		project_id: null,
	},
];

describe('Vendor RPC Functions', () => {
	let mockSupabase: ReturnType<typeof createMockSupabaseClient>;

	beforeEach(() => {
		vi.clearAllMocks();
		mockSupabase = createMockSupabaseClient({
			vendor: { data: mockVendors },
			organization: { data: mockOrganizations },
			client: { data: mockClients },
			project: { data: mockProjects },
			membership: { data: mockMemberships },
		});
	});

	describe('get_accessible_vendors RPC Function', () => {
		it('should return vendors accessible at organization level', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'organization' && entity_id_param === 'org-1') {
						// For organization scope, return only org-level vendors
						const accessibleVendors = mockVendors.filter(
							(v) => v.org_id === 'org-1' && v.is_active,
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								description: v.description,
								vendor_type: v.vendor_type,
								contact_name: v.contact_name,
								contact_email: v.contact_email,
								contact_phone: v.contact_phone,
								is_active: v.is_active,
								access_level: 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-1',
				entity_type_param: 'organization',
				entity_id_param: 'org-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(1); // Only active org-level vendor
			expect(data[0].vendor_id).toBe('vendor-1');
			expect(data[0].access_level).toBe('organization');
			expect(data[0].is_active).toBe(true);
		});

		it('should return vendors accessible at client level', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'client' && entity_id_param === 'client-1') {
						// For client scope, return client and org level vendors
						const accessibleVendors = mockVendors.filter(
							(v) => (v.client_id === 'client-1' || v.org_id === 'org-1') && v.is_active,
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								description: v.description,
								vendor_type: v.vendor_type,
								contact_name: v.contact_name,
								contact_email: v.contact_email,
								contact_phone: v.contact_phone,
								is_active: v.is_active,
								access_level: v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-2',
				entity_type_param: 'client',
				entity_id_param: 'client-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(2); // Org and client level vendors
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
		});

		it('should return vendors accessible at project level', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'project' && entity_id_param === 'project-1') {
						// For project scope, return project, client, and org level vendors
						const accessibleVendors = mockVendors.filter(
							(v) =>
								(v.project_id === 'project-1' ||
									v.client_id === 'client-1' ||
									v.org_id === 'org-1') &&
								v.is_active,
						);

						return Promise.resolve({
							data: accessibleVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								description: v.description,
								vendor_type: v.vendor_type,
								contact_name: v.contact_name,
								contact_email: v.contact_email,
								contact_phone: v.contact_phone,
								is_active: v.is_active,
								access_level: v.project_id ? 'project' : v.client_id ? 'client' : 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-3',
				entity_type_param: 'project',
				entity_id_param: 'project-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(3); // All three levels
			expect(data.some((v) => v.access_level === 'organization')).toBe(true);
			expect(data.some((v) => v.access_level === 'client')).toBe(true);
			expect(data.some((v) => v.access_level === 'project')).toBe(true);
		});

		it('should filter out inactive vendors', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { entity_type_param, entity_id_param } = params;

					if (entity_type_param === 'organization' && entity_id_param === 'org-1') {
						// Return all vendors for org-1, including inactive ones
						const allVendors = mockVendors.filter((v) => v.org_id === 'org-1');
						const activeVendors = allVendors.filter((v) => v.is_active);

						return Promise.resolve({
							data: activeVendors.map((v) => ({
								vendor_id: v.vendor_id,
								name: v.name,
								is_active: v.is_active,
								access_level: 'organization',
							})),
							error: null,
						});
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-1',
				entity_type_param: 'organization',
				entity_id_param: 'org-1',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(1); // Only active vendor
			expect(data.every((v) => v.is_active)).toBe(true);
		});

		it('should return empty array for user without access', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName, params) => {
				if (functionName === 'get_accessible_vendors') {
					const { user_id_param, entity_type_param, entity_id_param } = params;

					// Simulate access check failure
					if (
						user_id_param === 'user-3' &&
						entity_type_param === 'organization' &&
						entity_id_param === 'org-2'
					) {
						// User-3 has no access to org-2
						return Promise.resolve({ data: [], error: null });
					}
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_accessible_vendors', {
				user_id_param: 'user-3',
				entity_type_param: 'organization',
				entity_id_param: 'org-2',
			});

			expect(error).toBeNull();
			expect(data).toHaveLength(0);
		});
	});

	describe('get_vendor_creation_hierarchy RPC Function', () => {
		it('should return creation hierarchy for org admin', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName) => {
				if (functionName === 'get_vendor_creation_hierarchy') {
					// Simulate hierarchy for user-1 (org admin of org-1)
					return Promise.resolve({
						data: [
							{
								entity_type: 'organization',
								entity_id: 'org-1',
								entity_name: 'Organization 1',
								parent_entity_type: null,
								parent_entity_id: null,
								parent_entity_name: null,
								grandparent_entity_type: null,
								grandparent_entity_id: null,
								grandparent_entity_name: null,
								user_role: 'admin',
							},
							{
								entity_type: 'client',
								entity_id: 'client-1',
								entity_name: 'Client 1',
								parent_entity_type: 'organization',
								parent_entity_id: 'org-1',
								parent_entity_name: 'Organization 1',
								grandparent_entity_type: null,
								grandparent_entity_id: null,
								grandparent_entity_name: null,
								user_role: 'admin',
							},
							{
								entity_type: 'client',
								entity_id: 'client-2',
								entity_name: 'Client 2',
								parent_entity_type: 'organization',
								parent_entity_id: 'org-1',
								parent_entity_name: 'Organization 1',
								grandparent_entity_type: null,
								grandparent_entity_id: null,
								grandparent_entity_name: null,
								user_role: 'admin',
							},
							{
								entity_type: 'project',
								entity_id: 'project-1',
								entity_name: 'Project 1',
								parent_entity_type: 'client',
								parent_entity_id: 'client-1',
								parent_entity_name: 'Client 1',
								grandparent_entity_type: 'organization',
								grandparent_entity_id: 'org-1',
								grandparent_entity_name: 'Organization 1',
								user_role: 'admin',
							},
						],
						error: null,
					});
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_vendor_creation_hierarchy');

			expect(error).toBeNull();
			expect(data).toHaveLength(4);
			expect(data.some((item) => item.entity_type === 'organization')).toBe(true);
			expect(data.some((item) => item.entity_type === 'client')).toBe(true);
			expect(data.some((item) => item.entity_type === 'project')).toBe(true);
			expect(data.every((item) => item.user_role === 'admin')).toBe(true);
		});

		it('should return limited hierarchy for client admin', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName) => {
				if (functionName === 'get_vendor_creation_hierarchy') {
					// Simulate hierarchy for user-2 (client admin of client-1)
					return Promise.resolve({
						data: [
							{
								entity_type: 'client',
								entity_id: 'client-1',
								entity_name: 'Client 1',
								parent_entity_type: 'organization',
								parent_entity_id: 'org-1',
								parent_entity_name: 'Organization 1',
								grandparent_entity_type: null,
								grandparent_entity_id: null,
								grandparent_entity_name: null,
								user_role: 'admin',
							},
							{
								entity_type: 'project',
								entity_id: 'project-1',
								entity_name: 'Project 1',
								parent_entity_type: 'client',
								parent_entity_id: 'client-1',
								parent_entity_name: 'Client 1',
								grandparent_entity_type: 'organization',
								grandparent_entity_id: 'org-1',
								grandparent_entity_name: 'Organization 1',
								user_role: 'admin',
							},
						],
						error: null,
					});
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_vendor_creation_hierarchy');

			expect(error).toBeNull();
			expect(data).toHaveLength(2);
			expect(data.some((item) => item.entity_type === 'client')).toBe(true);
			expect(data.some((item) => item.entity_type === 'project')).toBe(true);
			expect(data.some((item) => item.entity_type === 'organization')).toBe(false);
		});

		it('should return minimal hierarchy for project editor', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName) => {
				if (functionName === 'get_vendor_creation_hierarchy') {
					// Simulate hierarchy for user-3 (project editor of project-1)
					return Promise.resolve({
						data: [
							{
								entity_type: 'project',
								entity_id: 'project-1',
								entity_name: 'Project 1',
								parent_entity_type: 'client',
								parent_entity_id: 'client-1',
								parent_entity_name: 'Client 1',
								grandparent_entity_type: 'organization',
								grandparent_entity_id: 'org-1',
								grandparent_entity_name: 'Organization 1',
								user_role: 'editor',
							},
						],
						error: null,
					});
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_vendor_creation_hierarchy');

			expect(error).toBeNull();
			expect(data).toHaveLength(1);
			expect(data[0].entity_type).toBe('project');
			expect(data[0].user_role).toBe('editor');
		});

		it('should return empty hierarchy for user with no permissions', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName) => {
				if (functionName === 'get_vendor_creation_hierarchy') {
					// Simulate no permissions
					return Promise.resolve({ data: [], error: null });
				}
				return Promise.resolve({ data: [], error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_vendor_creation_hierarchy');

			expect(error).toBeNull();
			expect(data).toHaveLength(0);
		});

		it('should handle RPC function errors gracefully', async () => {
			mockSupabase.rpc = vi.fn().mockImplementation((functionName) => {
				if (functionName === 'get_vendor_creation_hierarchy') {
					return Promise.resolve({
						data: null,
						error: { message: 'Function execution failed' },
					});
				}
				return Promise.resolve({ data: null, error: null });
			});

			const { data, error } = await mockSupabase.rpc('get_vendor_creation_hierarchy');

			expect(error).not.toBeNull();
			expect(error.message).toBe('Function execution failed');
			expect(data).toBeNull();
		});
	});
});
