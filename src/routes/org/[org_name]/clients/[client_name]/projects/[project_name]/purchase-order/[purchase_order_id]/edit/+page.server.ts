import { fail, error } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { editPurchaseOrderSchema } from '$lib/schemas/purchase_order';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name, purchase_order_id } = params;
	requireProject(params, cookies);

	// Fetch the purchase order with project and vendor information
	const { data: purchaseOrder, error: purchaseOrderError } = await supabase
		.from('purchase_order')
		.select(`
			*,
			project!inner(
				project_id,
				name,
				client!inner(
					name,
					organization!inner(name)
				)
			),
			vendor(vendor_id, name)
		`)
		.eq('purchase_order_id', purchase_order_id)
		.eq('project.client.organization.name', org_name)
		.eq('project.client.name', client_name)
		.eq('project.name', project_name)
		.single();

	if (purchaseOrderError || !purchaseOrder) {
		console.error('Error fetching purchase order:', purchaseOrderError);
		throw error(404, 'Purchase order not found');
	}

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		project_id_param: purchaseOrder.project.project_id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw new Error('Failed to fetch vendors');
	}

	// Prepare form data
	const formData = {
		purchase_order_id: purchaseOrder.purchase_order_id,
		po_number: purchaseOrder.po_number,
		description: purchaseOrder.description,
		po_date: purchaseOrder.po_date,
		vendor_id: purchaseOrder.vendor_id,
		account: purchaseOrder.account,
		original_amount: purchaseOrder.original_amount,
		co_amount: purchaseOrder.co_amount,
		freight: purchaseOrder.freight,
		tax: purchaseOrder.tax,
		other: purchaseOrder.other,
		notes: purchaseOrder.notes,
	};

	const form = await superValidate(formData, zod(editPurchaseOrderSchema));

	return {
		form,
		purchaseOrder,
		vendors: vendors || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		await requireUser(cookies);
		const { supabase } = locals;
		const { org_name, client_name, project_name, purchase_order_id } = params;

		const form = await superValidate(request, zod(editPurchaseOrderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Remove purchase_order_id from the update data
		const { purchase_order_id: _, ...updateData } = form.data;

		// Update the purchase order
		const { data: purchaseOrder, error: purchaseOrderError } = await supabase
			.from('purchase_order')
			.update(updateData)
			.eq('purchase_order_id', purchase_order_id)
			.select('purchase_order_id, po_number')
			.single();

		if (purchaseOrderError) {
			console.error('Error updating purchase order:', purchaseOrderError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update purchase order' },
			});
		}

		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/purchase-order`,
			{ type: 'success', message: `Purchase order ${purchaseOrder.po_number} updated successfully` },
			cookies,
		);
	},
};
