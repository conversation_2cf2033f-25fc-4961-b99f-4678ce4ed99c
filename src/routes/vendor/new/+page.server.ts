import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	vendorSchema,
	processVendorCreationHierarchy,
	type VendorCreationHierarchyItem,
} from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, cookies }) => {
	await requireUser(cookies);
	const { supabase } = locals;

	// Get vendor creation hierarchy using the optimized RPC function
	const { data: hierarchyData, error: hierarchyError } = await supabase.rpc(
		'get_vendor_creation_hierarchy',
	);

	if (hierarchyError) {
		console.error('Error fetching vendor creation hierarchy:', hierarchyError);
		throw new Error('Failed to fetch vendor creation hierarchy');
	}

	// Process the hierarchy data into UI-friendly format
	const { organizations, clients, projects } = processVendorCreationHierarchy(
		(hierarchyData as VendorCreationHierarchyItem[]) || [],
	);

	const form = await superValidate(zod(vendorSchema));

	return {
		form,
		organizations,
		clients,
		projects,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies }) => {
		const { user } = await requireUser(cookies);
		const { supabase } = locals;

		const form = await superValidate(request, zod(vendorSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Prepare the vendor data
		const vendorData = {
			...form.data,
			created_by_user_id: user.id,
		};

		// Insert the vendor
		const { data: vendor, error: vendorError } = await supabase
			.from('vendor')
			.insert(vendorData)
			.select('vendor_id, name')
			.single();

		if (vendorError) {
			console.error('Error creating vendor:', vendorError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create vendor' },
			});
		}

		throw redirect(
			'/vendor',
			{
				type: 'success',
				message: `Vendor "${vendor.name}" created successfully`,
			},
			cookies,
		);
	},
};
